// Shared utilities for all Supabase Edge Functions

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { ApiResponse, ValidationError } from './types.ts';

// Initialize Supabase client
export function createSupabaseClient() {
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  
  return createClient(supabaseUrl, supabaseServiceKey);
}

// CORS headers for all responses
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
};

// Standard error response
export function errorResponse(message: string, status = 400): Response {
  return new Response(
    JSON.stringify({
      success: false,
      error: message,
    } as ApiResponse),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  );
}

// Standard success response
export function successResponse<T>(data: T, message?: string): Response {
  return new Response(
    JSON.stringify({
      success: true,
      data,
      message,
    } as ApiResponse<T>),
    {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  );
}

// Validation error response
export function validationErrorResponse(errors: ValidationError[]): Response {
  return new Response(
    JSON.stringify({
      success: false,
      error: 'Validation failed',
      validation_errors: errors,
    }),
    {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  );
}

// Handle CORS preflight
export function handleCors(req: Request): Response | null {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  return null;
}

// Validate Israeli business number (9 digits)
export function validateBusinessNumber(businessNumber: string): boolean {
  const regex = /^[0-9]{9}$/;
  return regex.test(businessNumber);
}

// Validate Israeli VAT ID (9 digits)
export function validateVatId(vatId: string): boolean {
  const regex = /^[0-9]{9}$/;
  return regex.test(vatId);
}

// Validate Israeli phone number
export function validateIsraeliPhone(phone: string): boolean {
  const regex = /^(\+972|0)(5[0-9]|7[1-9])[0-9]{7}$/;
  return regex.test(phone);
}

// Validate email format
export function validateEmail(email: string): boolean {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

// Validate password strength
export function validatePassword(password: string): boolean {
  // At least 8 characters, 1 uppercase, 1 number
  const regex = /^(?=.*[A-Z])(?=.*\d).{8,}$/;
  return regex.test(password);
}

// Validate amount format
export function validateAmount(amount: string | number): boolean {
  const regex = /^\d+(\.\d{1,2})?$/;
  return regex.test(amount.toString());
}

// Get user from JWT token
export async function getUserFromToken(req: Request) {
  const authHeader = req.headers.get('authorization');
  if (!authHeader) {
    throw new Error('No authorization header');
  }

  const token = authHeader.replace('Bearer ', '');
  const supabase = createSupabaseClient();
  
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('Invalid token');
  }
  
  return user;
}

// Get user's company access
export async function getUserCompanyAccess(userId: string, companyId?: string) {
  const supabase = createSupabaseClient();
  
  let query = supabase
    .from('company_users')
    .select(`
      *,
      company:companies(*)
    `)
    .eq('user_id', userId);
    
  if (companyId) {
    query = query.eq('company_id', companyId);
  }
  
  const { data, error } = await query;
  
  if (error) {
    throw new Error('Failed to get company access');
  }
  
  return data;
}

// Check if user has role in company
export async function checkUserRole(userId: string, companyId: string, requiredRoles: string[]) {
  const access = await getUserCompanyAccess(userId, companyId);
  
  if (!access || access.length === 0) {
    throw new Error('No access to company');
  }
  
  const userRole = access[0].role;
  if (!requiredRoles.includes(userRole)) {
    throw new Error('Insufficient permissions');
  }
  
  return access[0];
}

// Generate next document number
export async function getNextDocumentNumber(companyId: string, documentType: string) {
  const supabase = createSupabaseClient();
  
  // Use a transaction to ensure atomicity
  const { data, error } = await supabase.rpc('get_next_document_number', {
    p_company_id: companyId,
    p_document_type: documentType
  });
  
  if (error) {
    throw new Error('Failed to generate document number');
  }
  
  return data;
}

// Calculate document totals
export function calculateDocumentTotals(items: any[]) {
  let subtotal = 0;
  let totalDiscount = 0;
  let vatAmount = 0;
  
  const calculatedItems = items.map((item, index) => {
    const baseAmount = item.quantity * item.unit_price;
    const discountAmount = baseAmount * (item.discount_percent || 0) / 100;
    const lineTotal = baseAmount - discountAmount;
    const itemVatAmount = lineTotal * (item.vat_rate || 18) / 100;
    const totalWithVat = lineTotal + itemVatAmount;
    
    subtotal += lineTotal;
    totalDiscount += discountAmount;
    vatAmount += itemVatAmount;
    
    return {
      ...item,
      line_number: index + 1,
      line_total: Math.round(lineTotal * 100) / 100,
      vat_amount: Math.round(itemVatAmount * 100) / 100,
      total_with_vat: Math.round(totalWithVat * 100) / 100,
    };
  });
  
  return {
    items: calculatedItems,
    subtotal: Math.round(subtotal * 100) / 100,
    total_discount: Math.round(totalDiscount * 100) / 100,
    vat_amount: Math.round(vatAmount * 100) / 100,
    total_amount: Math.round((subtotal + vatAmount) * 100) / 100,
  };
}

// Log audit trail
export async function logAudit(
  companyId: string,
  userId: string,
  action: string,
  entityType: string,
  entityId: string,
  oldValues?: any,
  newValues?: any,
  req?: Request
) {
  const supabase = createSupabaseClient();
  
  await supabase.from('audit_log').insert({
    company_id: companyId,
    user_id: userId,
    action,
    entity_type: entityType,
    entity_id: entityId,
    old_values: oldValues,
    new_values: newValues,
    ip_address: req?.headers.get('x-forwarded-for') || req?.headers.get('x-real-ip'),
    user_agent: req?.headers.get('user-agent'),
  });
}

// Rate limiting (simple in-memory implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(identifier: string, maxRequests: number, windowMs: number): boolean {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  const current = rateLimitMap.get(identifier);
  
  if (!current || current.resetTime < windowStart) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxRequests) {
    return false;
  }
  
  current.count++;
  return true;
}
