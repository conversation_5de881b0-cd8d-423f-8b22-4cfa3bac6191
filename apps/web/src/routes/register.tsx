import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { useState } from 'react'
import { Button } from '@fintech/ui'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@fintech/ui'
import { Input } from '@fintech/ui'
import { Label } from '@fintech/ui'
import { Progress } from '@fintech/ui'

interface AccountFormData {
  email: string
  password: string
  fullName: string
  phone: string
}

interface BusinessFormData {
  companyId: string
  nameHebrew: string
  nameEnglish: string
  addressHebrew: string
  cityHebrew: string
  phone: string
}

interface SurveyFormData {
  industry: string
  annualRevenue: string
  interestedInLoan: boolean
  interestedInInsurance: boolean
  interestedInAccounting: boolean
}

function RegisterPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  
  const [accountData, setAccountData] = useState<AccountFormData>({
    email: '',
    password: '',
    fullName: '',
    phone: ''
  })
  
  const [businessData, setBusinessData] = useState<BusinessFormData>({
    companyId: '',
    nameHebrew: '',
    nameEnglish: '',
    addressHebrew: '',
    cityHebrew: '',
    phone: ''
  })
  
  const [surveyData, setSurveyData] = useState<SurveyFormData>({
    industry: '',
    annualRevenue: '',
    interestedInLoan: false,
    interestedInInsurance: false,
    interestedInAccounting: false
  })

  const steps = ['סקר קצר', 'פרטי עסק', 'פרטי חשבון']
  const progress = (currentStep / steps.length) * 100

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSubmit = async () => {
    setIsLoading(true)
    try {
      // TODO: Implement Supabase registration
      console.log('Registration data:', { accountData, businessData, surveyData })
    } catch (error) {
      console.error('Registration error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const renderStep1 = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="email">כתובת אימייל</Label>
        <Input
          id="email"
          type="email"
          value={accountData.email}
          onChange={(e) => setAccountData({...accountData, email: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="<EMAIL>"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="password">סיסמה</Label>
        <Input
          id="password"
          type="password"
          value={accountData.password}
          onChange={(e) => setAccountData({...accountData, password: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="••••••••"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="fullName">שם מלא</Label>
        <Input
          id="fullName"
          value={accountData.fullName}
          onChange={(e) => setAccountData({...accountData, fullName: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="שם פרטי ומשפחה"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="phone">טלפון</Label>
        <Input
          id="phone"
          type="tel"
          value={accountData.phone}
          onChange={(e) => setAccountData({...accountData, phone: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="050-1234567"
          required
        />
      </div>
    </div>
  )

  const renderStep2 = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="companyId">ע.מ / ח.פ</Label>
        <Input
          id="companyId"
          value={businessData.companyId}
          onChange={(e) => setBusinessData({...businessData, companyId: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="*********"
          pattern="[0-9]{9}"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="nameHebrew">שם העסק (עברית)</Label>
        <Input
          id="nameHebrew"
          value={businessData.nameHebrew}
          onChange={(e) => setBusinessData({...businessData, nameHebrew: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="שם החברה בעברית"
          required
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="nameEnglish">שם העסק (אנגלית)</Label>
        <Input
          id="nameEnglish"
          value={businessData.nameEnglish}
          onChange={(e) => setBusinessData({...businessData, nameEnglish: e.target.value})}
          className="text-left"
          dir="ltr"
          placeholder="Company Name in English"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="addressHebrew">כתובת</Label>
        <Input
          id="addressHebrew"
          value={businessData.addressHebrew}
          onChange={(e) => setBusinessData({...businessData, addressHebrew: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="רחוב, מספר בית, עיר"
          required
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="cityHebrew">עיר</Label>
        <Input
          id="cityHebrew"
          value={businessData.cityHebrew}
          onChange={(e) => setBusinessData({...businessData, cityHebrew: e.target.value})}
          className="text-right"
          dir="rtl"
          placeholder="תל אביב"
          required
        />
      </div>
    </div>
  )

  const renderStep3 = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="industry">תחום עיסוק</Label>
        <select
          id="industry"
          value={surveyData.industry}
          onChange={(e) => setSurveyData({...surveyData, industry: e.target.value})}
          className="w-full p-2 border border-input bg-background rounded-md text-right"
          dir="rtl"
          required
        >
          <option value="">בחר תחום</option>
          <option value="technology">טכנולוגיה</option>
          <option value="retail">קמעונאות</option>
          <option value="services">שירותים</option>
          <option value="manufacturing">ייצור</option>
          <option value="construction">בנייה</option>
          <option value="healthcare">בריאות</option>
          <option value="other">אחר</option>
        </select>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="annualRevenue">מחזור שנתי</Label>
        <select
          id="annualRevenue"
          value={surveyData.annualRevenue}
          onChange={(e) => setSurveyData({...surveyData, annualRevenue: e.target.value})}
          className="w-full p-2 border border-input bg-background rounded-md text-right"
          dir="rtl"
        >
          <option value="">בחר טווח</option>
          <option value="< 500K">פחות מ-500 אלף ₪</option>
          <option value="500K-1M">500 אלף - 1 מיליון ₪</option>
          <option value="1M-5M">1-5 מיליון ₪</option>
          <option value="5M-10M">5-10 מיליון ₪</option>
          <option value="10M+">מעל 10 מיליון ₪</option>
        </select>
      </div>
      
      <div className="space-y-3">
        <Label>האם אתה מעוניין ב:</Label>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <input
            type="checkbox"
            id="loan"
            checked={surveyData.interestedInLoan}
            onChange={(e) => setSurveyData({...surveyData, interestedInLoan: e.target.checked})}
            className="rounded"
          />
          <Label htmlFor="loan">הלוואות עסקיות</Label>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <input
            type="checkbox"
            id="insurance"
            checked={surveyData.interestedInInsurance}
            onChange={(e) => setSurveyData({...surveyData, interestedInInsurance: e.target.checked})}
            className="rounded"
          />
          <Label htmlFor="insurance">ביטוח עסקי</Label>
        </div>
        
        <div className="flex items-center space-x-2 space-x-reverse">
          <input
            type="checkbox"
            id="accounting"
            checked={surveyData.interestedInAccounting}
            onChange={(e) => setSurveyData({...surveyData, interestedInAccounting: e.target.checked})}
            className="rounded"
          />
          <Label htmlFor="accounting">שירותי הנהלת חשבונות</Label>
        </div>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen flex items-center justify-center bg-background cosmic-grid px-4">
      <Card className="w-full max-w-2xl cosmic-glass">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold font-secular">הרשמה</CardTitle>
          <CardDescription>
            צור חשבון חדש למערכת החשבוניות
          </CardDescription>
          
          {/* Progress Indicator */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-muted-foreground mb-2" dir="rtl">
              {steps.map((step, index) => (
                <span
                  key={index}
                  className={currentStep > (steps.length - 1 - index) ? 'text-primary font-medium' : ''}
                >
                  {step}
                </span>
              ))}
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        </CardHeader>
        
        <CardContent>
          {currentStep === 1 && renderStep1()}
          {currentStep === 2 && renderStep2()}
          {currentStep === 3 && renderStep3()}
          
          <div className="flex justify-between mt-6">
            <Button
              variant="outline"
              onClick={handleBack}
              disabled={currentStep === 1}
            >
              הקודם
            </Button>
            
            {currentStep < 3 ? (
              <Button onClick={handleNext}>
                הבא
              </Button>
            ) : (
              <Button onClick={handleSubmit} disabled={isLoading}>
                {isLoading ? 'נרשם...' : 'הירשם'}
              </Button>
            )}
          </div>
          
          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              יש לך כבר חשבון?{' '}
              <Link 
                to="/login" 
                className="text-primary hover:underline font-medium"
              >
                התחבר כאן
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const Route = createFileRoute('/register')({
  component: RegisterPage,
})
